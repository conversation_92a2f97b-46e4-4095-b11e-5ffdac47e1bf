/**
 * Item Manager
 * Handles receipt items CRUD operations and drag-and-drop functionality
 */

class ItemManager {
    constructor() {
        this.receiptItems = [];
        this.isDragDropEnabled = true;
        this.compactView = true;
    }

    /**
     * Add Item to Receipt
     */
    addItemToReceipt(item) {
        // Price priority: Special price (>0 and less than original if exists) -> Default price -> Original price -> 0
        const hasNum = (v) => typeof v === 'number' && !isNaN(v);
        const useSpecial = hasNum(item?.special_price) && item.special_price > 0 && (!hasNum(item?.original_price) || item.special_price < item.original_price);
        const basePrice = useSpecial
            ? item.special_price
            : (hasNum(item?.default_price) ? item.default_price : (hasNum(item?.original_price) ? item.original_price : 0));

        // Calculate discount percentage
        let discountPercent = 0;
        if (hasNum(item?.original_price) && hasNum(item?.special_price) && item.original_price > 0) {
            discountPercent = Math.round(((item.original_price - item.special_price) / item.original_price) * 100);
        }

        const newItem = {
            id: Date.now(),
            name: item.name,
            category: item.category,
            description: item.description || '',
            quantity: 1,
            unitPrice: parseFloat(basePrice) || 0,
            totalPrice: parseFloat(basePrice) || 0,
            originalPrice: parseFloat(item.original_price) || 0,
            specialPrice: parseFloat(item.special_price) || 0,
            discountPercent: discountPercent,
            hidePrice: false
        };

        this.receiptItems.push(newItem);
        this.updateReceiptItemsDisplay();
        this.updateTotals();
        
        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }

        // Show success message
        if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
            UIManager.showMessage('Item added successfully!', 'success');
        } else if (typeof showMessage === 'function') {
            showMessage('Item added successfully!', 'success');
        }
    }

    /**
     * Remove Item from Receipt
     */
    removeItemFromReceipt(itemId) {
        this.receiptItems = this.receiptItems.filter(item => item.id !== itemId);
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Update Item Quantity
     */
    updateItemQuantity(itemId, quantity) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            const oldQuantity = item.quantity;
            const oldTotalPrice = item.totalPrice;

            item.quantity = Math.max(1, parseInt(quantity) || 1);
            item.totalPrice = item.quantity * item.unitPrice;

            console.log(`Quantity updated for item ${itemId}: ${oldQuantity} -> ${item.quantity}, Total: ${oldTotalPrice} -> ${item.totalPrice}`);

            this.updateReceiptItemsDisplay();
            this.updateTotals();

            // Force update the receipt preview with fresh data
            if (typeof window.ReceiptGenerator !== 'undefined' && window.ReceiptGenerator.updateReceiptPreview) {
                setTimeout(() => {
                    window.ReceiptGenerator.updateReceiptPreview();
                }, 100); // Small delay to ensure DOM updates are complete
            }
        }
    }

    /**
     * Update Item Price
     */
    updateItemPrice(itemId, price) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            item.unitPrice = Math.max(0, parseFloat(price) || 0);
            item.totalPrice = item.quantity * item.unitPrice;
            this.updateReceiptItemsDisplay();
            this.updateTotals();
            
            if (typeof window.ReceiptGenerator !== 'undefined' && window.ReceiptGenerator.updateReceiptPreview) {
                window.ReceiptGenerator.updateReceiptPreview();
            }
        }
    }

    /**
     * Toggle Item Price Display
     */
    toggleItemPriceDisplay(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            item.hidePrice = !item.hidePrice;
            this.updateReceiptItemsDisplay();

            if (typeof window.ReceiptGenerator !== 'undefined' && window.ReceiptGenerator.updateReceiptPreview) {
                window.ReceiptGenerator.updateReceiptPreview();
            }
        }
    }

    /**
     * Render Price Information
     */
    renderPriceInfo(item) {
        if (!item.originalPrice && !item.specialPrice) {
            return '';
        }

        let priceHtml = '<div class="item-price-info mt-1">';

        const originalPrice = parseFloat(item.originalPrice) || 0;
        const specialPrice = parseFloat(item.specialPrice) || 0;
        const discountPercent = parseInt(item.discountPercent) || 0;

        if (originalPrice > 0 && specialPrice > 0 && specialPrice < originalPrice) {
            // Show original price crossed out and special price
            priceHtml += `
                <div class="price-display">
                    <span class="original-price text-muted text-decoration-line-through">$${originalPrice.toFixed(2)}</span>
                    <span class="special-price text-danger fw-bold ms-2">$${specialPrice.toFixed(2)}</span>
                    ${discountPercent > 0 ? `<span class="discount-badge badge bg-success ms-2">-${discountPercent}%</span>` : ''}
                </div>
            `;
        } else if (originalPrice > 0) {
            priceHtml += `<div class="price-display"><span class="original-price">$${originalPrice.toFixed(2)}</span></div>`;
        }

        priceHtml += '</div>';
        return priceHtml;
    }

    /**
     * Edit Item
     */
    editItem(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (!item) return;

        // Create edit modal HTML with same styling as Add Item Modal
        const modalHtml = `
            <div class="modal fade" id="editItemModal" tabindex="-1" aria-labelledby="editItemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content add-item-modal-content">
                        <header class="modal-header add-item-modal-header">
                            <div class="modal-title-container">
                                <div class="modal-icon">
                                    <span class="icon-symbol">✏️</span>
                                </div>
                                <h5 class="modal-title" id="editItemModalLabel">
                                    <span data-lang="edit_item">Edit Item</span>
                                </h5>
                            </div>
                            <button type="button" class="btn-close add-item-modal-close" data-bs-dismiss="modal" aria-label="Close">
                                <span class="icon-symbol">✕</span>
                            </button>
                        </header>
                        <div class="modal-body add-item-modal-body">
                            <form id="editItemModalForm" class="add-item-form">
                                <!-- Basic Information Section -->
                                <div class="form-section">
                                    <h6 class="section-title">
                                        <span class="icon-symbol">📝</span>
                                        <span data-lang="basic_info">Basic Information</span>
                                    </h6>
                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="editItemName" class="form-label">
                                                <span class="icon-symbol">🏷️</span>
                                                <span data-lang="item_name">Item Name</span>
                                                <span class="required">*</span>
                                            </label>
                                            <input type="text" class="form-control modern-input" id="editItemName" required placeholder="Enter item name..." value="${item.name}">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="editItemCategory" class="form-label">
                                                <span class="icon-symbol">📂</span>
                                                <span data-lang="item_category">Category</span>
                                            </label>
                                            <select class="form-select modern-select" id="editItemCategory">
                                                <option value="">Select Category</option>
                                                <option value="PC Case" ${item.category === 'PC Case' ? 'selected' : ''}>🖥️ PC Case</option>
                                                <option value="CPU" ${item.category === 'CPU' ? 'selected' : ''}>🔧 CPU</option>
                                                <option value="CPU Cooler" ${item.category === 'CPU Cooler' ? 'selected' : ''}>❄️ CPU Cooler</option>
                                                <option value="GPU" ${item.category === 'GPU' ? 'selected' : ''}>🎮 GPU</option>
                                                <option value="RAM" ${item.category === 'RAM' ? 'selected' : ''}>💾 RAM</option>
                                                <option value="SSD" ${item.category === 'SSD' ? 'selected' : ''}>💿 SSD</option>
                                                <option value="Motherboard" ${item.category === 'Motherboard' ? 'selected' : ''}>🔌 Motherboard</option>
                                                <option value="PSU" ${item.category === 'PSU' ? 'selected' : ''}>⚡ PSU</option>
                                                <option value="MB RGB" ${item.category === 'MB RGB' ? 'selected' : ''}>🌈 MB RGB</option>
                                                <option value="GPU RGB" ${item.category === 'GPU RGB' ? 'selected' : ''}>🎨 GPU RGB</option>
                                                <option value="Fan RGB" ${item.category === 'Fan RGB' ? 'selected' : ''}>💨 Fan RGB</option>
                                                <option value="Other" ${item.category === 'Other' ? 'selected' : ''}>📦 Other</option>
                                                <option value="Services" ${item.category === 'Services' ? 'selected' : ''}>🛠️ Services</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="editItemDescription" class="form-label">
                                                <span class="icon-symbol">📄</span>
                                                <span data-lang="item_description">Description</span>
                                            </label>
                                            <textarea class="form-control modern-textarea" id="editItemDescription" rows="4" placeholder="Enter item description...">${item.description || ''}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pricing Section -->
                                <div class="form-section">
                                    <h6 class="section-title">
                                        <span class="icon-symbol">💰</span>
                                        <span data-lang="pricing_info">Pricing Information</span>
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="editItemQuantity" class="form-label">
                                                <span class="icon-symbol">🔢</span>
                                                <span data-lang="quantity">Quantity</span>
                                                <span class="required">*</span>
                                            </label>
                                            <input type="number" class="form-control modern-input" id="editItemQuantity" min="1" value="${item.quantity}" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="editItemOriginalPrice" class="form-label">
                                                <span class="icon-symbol">🏷️</span>
                                                <span data-lang="original_price">Original Price</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control modern-input" id="editItemOriginalPrice" min="0" step="0.01" placeholder="0.00" value="${item.originalPrice || item.unitPrice}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="editItemSpecialPrice" class="form-label">
                                                <span class="icon-symbol">💸</span>
                                                <span data-lang="special_price">Special Price</span>
                                                <span class="required">*</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control modern-input" id="editItemSpecialPrice" min="0" step="0.01" required placeholder="0.00" value="${item.specialPrice || item.unitPrice}">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="editItemDiscountPercent" class="form-label">
                                                <span class="icon-symbol">🏷️</span>
                                                <span data-lang="discount_percentage">Discount %</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control modern-input" id="editItemDiscountPercent" readonly value="${item.discountPercent || 0}">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="editItemTotal" class="form-label">
                                                <span class="icon-symbol">💵</span>
                                                <span data-lang="total_price">Total Price</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control modern-input" id="editItemTotal" readonly value="${item.totalPrice}">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">
                                                <span class="icon-symbol">👁️</span>
                                                <span data-lang="display_options">Display Options</span>
                                            </label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="editItemHidePrice" ${item.hidePrice ? 'checked' : ''}>
                                                <label class="form-check-label" for="editItemHidePrice" data-lang="hide_price">
                                                    Hide Price (Show N/A on receipt)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <footer class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <span class="icon-symbol">✕</span>
                                <span data-lang="cancel">Cancel</span>
                            </button>
                            <button type="button" class="btn btn-success" onclick="saveItemEdit(${itemId})">
                                <span class="icon-symbol">✓</span>
                                <span data-lang="save_changes">Save Changes</span>
                            </button>
                        </footer>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('editItemModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = Modal.getInstance(document.getElementById('editItemModal'));
        modal.show();
    }

    /**
     * Save Item Edit
     */
    saveItemEdit(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (!item) return;

        // Get values from form
        const name = document.getElementById('editItemName').value.trim();
        const category = document.getElementById('editItemCategory').value.trim();
        const description = document.getElementById('editItemDescription').value.trim();
        const originalPrice = parseFloat(document.getElementById('editItemOriginalPrice').value) || 0;
        const specialPrice = parseFloat(document.getElementById('editItemSpecialPrice').value) || 0;
        const quantity = parseInt(document.getElementById('editItemQuantity').value) || 1;
        const hidePrice = document.getElementById('editItemHidePrice').checked;

        // Validation
        if (!name) {
            alert('Please enter item name');
            return;
        }

        if (specialPrice <= 0) {
            alert('Please enter a valid special price');
            return;
        }

        // Calculate discount percentage
        let discountPercent = 0;
        if (originalPrice > 0 && specialPrice > 0 && originalPrice > specialPrice) {
            discountPercent = Math.round(((originalPrice - specialPrice) / originalPrice) * 100);
        }

        // Update item
        item.name = name;
        item.category = category;
        item.description = description;
        item.originalPrice = originalPrice;
        item.specialPrice = specialPrice;
        item.discountPercent = discountPercent;
        item.quantity = quantity;
        item.unitPrice = specialPrice; // Use special price as unit price
        item.totalPrice = quantity * specialPrice;
        item.hidePrice = hidePrice;

        // Update display
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof window.ReceiptGenerator !== 'undefined' && window.ReceiptGenerator.updateReceiptPreview) {
            window.ReceiptGenerator.updateReceiptPreview();
        }

        // Close modal
        const modal = Modal.getInstance(document.getElementById('editItemModal'));
        if (modal) {
            modal.hide();
        }

        // Show success message
        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Item updated successfully!', 'success');
        }
    }

    /**
     * Move Item (for drag and drop)
     */
    moveItem(fromIndex, toIndex) {
        if (fromIndex < 0 || fromIndex >= this.receiptItems.length || 
            toIndex < 0 || toIndex >= this.receiptItems.length) {
            return;
        }

        const item = this.receiptItems.splice(fromIndex, 1)[0];
        this.receiptItems.splice(toIndex, 0, item);
        
        this.updateReceiptItemsDisplay();
        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Update Receipt Items Display
     */
    updateReceiptItemsDisplay() {
        const container = document.getElementById('receiptItemsList');
        if (!container) return;

        if (this.receiptItems.length === 0) {
            container.innerHTML = `
                <div class="receipt-items-empty">
                    <span class="icon-symbol large">📦</span>
                    <p>No items added yet</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.receiptItems.map((item, index) => `
            <div class="receipt-item ${this.isDragDropEnabled ? 'draggable' : ''}" 
                 data-item-id="${item.id}" 
                 data-index="${index}"
                 draggable="${this.isDragDropEnabled}">
                ${this.isDragDropEnabled ? '<div class="drag-handle"><span class="icon-symbol">⋮⋮</span></div>' : ''}
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="item-info">
                            <h6>${item.name}</h6>
                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Original Price</label>
                        <div class="item-price-display">${item.originalPrice ? '$' + item.originalPrice.toFixed(2) : 'N/A'}</div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">Discount</label>
                        <div class="item-discount-display">${item.discountPercent ? item.discountPercent + '%' : 'N/A'}</div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Special Price</label>
                        <div class="item-special-price">${item.specialPrice ? '$' + item.specialPrice.toFixed(2) : '$' + item.unitPrice.toFixed(2)}</div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Total Price</label>
                        <div class="item-total-price">$${item.totalPrice.toFixed(2)}</div>
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="item-actions d-flex flex-column gap-1">
                            <button class="btn btn-action btn-action-edit btn-xs"
                                    onclick="editItem(${item.id})"
                                    title="Edit Item">
                                <span class="icon-symbol">✏</span>
                            </button>
                            <button class="btn btn-action btn-action-toggle btn-xs"
                                    onclick="toggleItemPriceDisplay(${item.id})"
                                    title="${item.hidePrice ? 'Show Price' : 'Hide Price'}">
                                <span class="icon-symbol">${item.hidePrice ? '👁' : '🙈'}</span>
                            </button>
                            <button class="btn btn-action btn-action-delete btn-xs"
                                    onclick="removeItemFromReceipt(${item.id})"
                                    title="Remove Item">
                                <span class="icon-symbol">🗑</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Initialize drag and drop if enabled
        if (this.isDragDropEnabled) {
            this.initializeDragAndDrop();
        }
    }

    /**
     * Initialize Drag and Drop
     */
    initializeDragAndDrop() {
        const container = document.getElementById('receiptItemsList');
        if (!container) return;

        const items = container.querySelectorAll('.receipt-item.draggable');
        
        items.forEach(item => {
            item.addEventListener('dragstart', this.handleDragStart.bind(this));
            item.addEventListener('dragover', this.handleDragOver.bind(this));
            item.addEventListener('drop', this.handleDrop.bind(this));
            item.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
    }

    /**
     * Handle Drag Start
     */
    handleDragStart(e) {
        e.dataTransfer.setData('text/plain', e.target.dataset.index);
        e.target.classList.add('dragging');
    }

    /**
     * Handle Drag Over
     */
    handleDragOver(e) {
        e.preventDefault();
        e.target.closest('.receipt-item').classList.add('drag-over');
    }

    /**
     * Handle Drop
     */
    handleDrop(e) {
        e.preventDefault();
        const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
        const toIndex = parseInt(e.target.closest('.receipt-item').dataset.index);
        
        if (fromIndex !== toIndex) {
            this.moveItem(fromIndex, toIndex);
        }
        
        // Clean up drag classes
        document.querySelectorAll('.receipt-item').forEach(item => {
            item.classList.remove('drag-over');
        });
    }

    /**
     * Handle Drag End
     */
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        document.querySelectorAll('.receipt-item').forEach(item => {
            item.classList.remove('drag-over');
        });
    }

    /**
     * Update Totals
     */
    updateTotals() {
        console.log('Updating totals for items:', this.receiptItems.length);

        const subtotal = this.receiptItems.reduce((sum, item) => {
            const itemTotal = item.totalPrice || 0;
            console.log(`Item: ${item.name}, Total: ${itemTotal}`);
            return sum + itemTotal;
        }, 0);

        console.log('Calculated subtotal:', subtotal);

        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        console.log('Final totals:', { subtotal, discountAmount, taxAmount, total });

        this.updateTotalsDisplay(subtotal, discountAmount, taxAmount, total);
    }

    /**
     * Update Totals Display
     */
    updateTotalsDisplay(subtotal, discount, tax, total) {
        const totalsContainer = document.getElementById('receiptTotals');
        if (totalsContainer) {
            totalsContainer.innerHTML = `
                <div class="receipt-totals-summary">
                    <div class="row">
                        <div class="col-6">Subtotal:</div>
                        <div class="col-6">$${subtotal.toFixed(2)}</div>
                    </div>
                    ${discount > 0 ? `
                    <div class="row">
                        <div class="col-6">Discount:</div>
                        <div class="col-6 text-danger">-$${discount.toFixed(2)}</div>
                    </div>
                    ` : ''}
                    ${tax > 0 ? `
                    <div class="row">
                        <div class="col-6">Tax:</div>
                        <div class="col-6">$${tax.toFixed(2)}</div>
                    </div>
                    ` : ''}
                    <div class="row">
                        <div class="col-6">Total:</div>
                        <div class="col-6">$${total.toFixed(2)}</div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Calculate Totals
     */
    calculateTotals() {
        const subtotal = this.receiptItems.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total
        };
    }

    /**
     * Clear All Items
     */
    async clearAllItems() {
        if (this.receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('No items to clear', 'info');
            }
            return;
        }

        // Use beautiful custom modal for clear all confirmation
        const confirmed = await window.showClearAllConfirm(
            window.LanguageManager ?
                window.LanguageManager.getText('confirm_clear_all_items') || 'Are you sure you want to clear all items? This action cannot be undone.' :
                'Are you sure you want to clear all items? This action cannot be undone.',
            window.LanguageManager ?
                window.LanguageManager.getText('clear_all_items') || 'Clear All Items' :
                'Clear All Items'
        );

        if (confirmed) {
            this.receiptItems = [];
            this.updateReceiptItemsDisplay();
            this.updateTotals();

            if (typeof window.ReceiptGenerator !== 'undefined' && window.ReceiptGenerator.updateReceiptPreview) {
                window.ReceiptGenerator.updateReceiptPreview();
            }

            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage(
                    window.LanguageManager ?
                        window.LanguageManager.getText('all_items_cleared') || 'All items cleared successfully' :
                        'All items cleared successfully',
                    'success'
                );
            }
        }
    }

    /**
     * Get receipt items
     */
    getReceiptItems() {
        return this.receiptItems;
    }

    /**
     * Clear all receipt items
     */
    clearReceiptItems() {
        this.receiptItems = [];
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Add receipt item (alias for addItemToReceipt)
     */
    addReceiptItem(item) {
        return this.addItemToReceipt(item);
    }

    /**
     * Set receipt items
     */
    setReceiptItems(items) {
        this.receiptItems = items || [];
        this.updateReceiptItemsDisplay();
        this.updateTotals();
    }

    /**
     * Toggle compact view
     */
    toggleCompactView() {
        this.compactView = !this.compactView;
        const container = document.getElementById('receiptItemsList');
        if (container) {
            container.classList.toggle('compact-view', this.compactView);
        }
    }

    /**
     * Toggle drag and drop
     */
    toggleDragAndDrop() {
        this.isDragDropEnabled = !this.isDragDropEnabled;
        this.updateReceiptItemsDisplay();
    }

    /**
     * Show Add Item Modal
     */
    showAddItemModal() {
        // Clear form
        const nameEl = document.getElementById('modalItemName');
        const categoryEl = document.getElementById('modalItemCategory');
        const descEl = document.getElementById('modalItemDescription');
        const quantityEl = document.getElementById('modalItemQuantity');
        const originalPriceEl = document.getElementById('modalItemOriginalPrice');
        const specialPriceEl = document.getElementById('modalItemSpecialPrice');
        const discountPercentEl = document.getElementById('modalItemDiscountPercent');
        const totalEl = document.getElementById('modalItemTotal');
        const hidePriceEl = document.getElementById('modalItemHidePrice');

        if (nameEl) nameEl.value = '';
        if (categoryEl) categoryEl.value = 'PC Case';
        if (descEl) descEl.value = '';
        if (quantityEl) quantityEl.value = '1';
        if (originalPriceEl) originalPriceEl.value = '';
        if (specialPriceEl) specialPriceEl.value = '';
        if (discountPercentEl) discountPercentEl.value = '';
        if (totalEl) totalEl.value = '';
        if (hidePriceEl) hidePriceEl.checked = false;

        // Add event listeners for automatic calculation
        if (quantityEl && originalPriceEl && specialPriceEl && window.calculateModalItemTotal) {
            // Remove existing listeners first to avoid duplicates
            quantityEl.removeEventListener('input', window.calculateModalItemTotal);
            originalPriceEl.removeEventListener('input', window.calculateModalItemTotal);
            specialPriceEl.removeEventListener('input', window.calculateModalItemTotal);

            // Add new listeners
            quantityEl.addEventListener('input', window.calculateModalItemTotal);
            originalPriceEl.addEventListener('input', window.calculateModalItemTotal);
            specialPriceEl.addEventListener('input', window.calculateModalItemTotal);
        }

        // Show modal
        const modal = document.getElementById('addItemModal');
        if (modal) {
            const customModal = Modal.getInstance(modal);
            customModal.show();
        } else {
            console.error('Add item modal not found');
        }
    }

    /**
     * Confirm Add Modal Item
     */
    confirmAddModalItem() {
        const nameEl = document.getElementById('modalItemName');
        const categoryEl = document.getElementById('modalItemCategory');
        const descEl = document.getElementById('modalItemDescription');
        const quantityEl = document.getElementById('modalItemQuantity');
        const originalPriceEl = document.getElementById('modalItemOriginalPrice');
        const specialPriceEl = document.getElementById('modalItemSpecialPrice');
        const hidePriceEl = document.getElementById('modalItemHidePrice');

        if (!nameEl || !categoryEl || !quantityEl || !specialPriceEl) {
            alert('Required form elements not found');
            return;
        }

        const name = nameEl.value.trim();
        const category = categoryEl.value;
        const description = descEl ? descEl.value.trim() : '';
        const quantity = parseInt(quantityEl.value) || 1;
        const originalPrice = originalPriceEl ? parseFloat(originalPriceEl.value) || 0 : 0;
        const specialPrice = parseFloat(specialPriceEl.value) || 0;
        const hidePrice = hidePriceEl ? hidePriceEl.checked : false;

        if (!name) {
            alert('Please enter item name');
            return;
        }

        if (specialPrice <= 0) {
            alert('Please enter a valid price');
            return;
        }

        // Calculate discount percentage
        let discountPercent = 0;
        if (originalPrice > 0 && specialPrice < originalPrice) {
            discountPercent = Math.round(((originalPrice - specialPrice) / originalPrice) * 100);
        }

        // Create new item
        const newItem = {
            id: Date.now(),
            name: name,
            category: category,
            description: description,
            quantity: quantity,
            unitPrice: specialPrice,
            totalPrice: quantity * specialPrice,
            originalPrice: originalPrice,
            specialPrice: specialPrice,
            discountPercent: discountPercent,
            hidePrice: hidePrice
        };

        // Add to receipt items
        this.receiptItems.push(newItem);
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        }

        // Close modal
        const modal = Modal.getInstance(document.getElementById('addItemModal'));
        if (modal) {
            modal.hide();
        }

        // Show success message
        if (typeof window.UIManager !== 'undefined' && window.UIManager.showMessage) {
            window.UIManager.showMessage('Item added successfully!', 'success');
        } else if (typeof window.showMessage === 'function') {
            window.showMessage('Item added successfully!', 'success');
        }
    }
}

// Create global instance
window.ItemManager = new ItemManager();

// Export functions for backward compatibility
Object.defineProperty(window, 'receiptItems', {
    get: () => window.ItemManager.receiptItems,
    set: (value) => window.ItemManager.setReceiptItems(value)
});
window.addItemToReceipt = (item) => window.ItemManager.addItemToReceipt(item);
window.removeItemFromReceipt = (itemId) => window.ItemManager.removeItemFromReceipt(itemId);
window.updateItemQuantity = (itemId, quantity) => window.ItemManager.updateItemQuantity(itemId, quantity);
window.updateItemPrice = (itemId, price) => window.ItemManager.updateItemPrice(itemId, price);
window.toggleItemPriceDisplay = (itemId) => window.ItemManager.toggleItemPriceDisplay(itemId);
window.updateReceiptItemsDisplay = () => window.ItemManager.updateReceiptItemsDisplay();
window.updateTotals = () => window.ItemManager.updateTotals();
window.calculateTotals = () => window.ItemManager.calculateTotals();
window.clearAllItems = () => window.ItemManager.clearAllItems();
window.showAddItemModal = () => window.ItemManager.showAddItemModal();
window.confirmAddModalItem = () => window.ItemManager.confirmAddModalItem();
window.editItem = (itemId) => window.ItemManager.editItem(itemId);
window.saveItemEdit = (itemId) => window.ItemManager.saveItemEdit(itemId);
