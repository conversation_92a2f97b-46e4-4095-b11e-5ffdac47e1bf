/* Receipt Details Modal - Professional Design */

/* Modal Container - Increased width by 30% */
.receipt-details-modal .modal-dialog {
    max-width: 1560px; /* Increased from 1200px by 30% */
    margin: 2rem auto;
}

.receipt-details-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Header Styles */
.receipt-details-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    padding: 1.5rem 2rem;
    color: white;
    position: relative;
}

.receipt-header-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.receipt-title {
    align-items: center;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.receipt-icon {
    font-size: 1.8rem;
    margin-right: 0.75rem;
}

.receipt-title-text {
    color: white;
}

.receipt-number-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.receipt-number-label {
    font-size: 0.8rem;
    opacity: 0.9;
    display: block;
    margin-bottom: 0.2rem;
}

.receipt-number-value {
    font-size: 1rem;
    font-weight: 700;
    color: white;
}

.receipt-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.receipt-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Body Styles - Golden background */
.receipt-details-body {
    padding: 2rem;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.receipt-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Info Cards - Enhanced with golden theme */
.receipt-info-card {
    background: linear-gradient(135deg, #FFFACD 0%, #F5DEB3 100%);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(218, 165, 32, 0.2);
    border: 2px solid rgba(218, 165, 32, 0.3);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.receipt-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(218, 165, 32, 0.3);
    border-color: rgba(218, 165, 32, 0.5);
}

.card-header-modern {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    color: white;
}

.customer-info-card .card-header-modern {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.receipt-summary-card .card-header-modern {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.items-card .card-header-modern {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.card-title-modern {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-body-modern {
    padding: 1.5rem;
}

/* Info Rows */
.info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #4a5568;
    min-width: 80px;
    font-size: 0.9rem;
}

.info-value {
    color: #2d3748;
    text-align: right;
    flex: 1;
    margin-left: 1rem;
    word-break: break-word;
}

/* Payment Method Badge */
.payment-method-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Financial Summary */
.financial-summary {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid rgba(0, 0, 0, 0.1);
}

.financial-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.financial-label {
    font-weight: 500;
    color: #4a5568;
}

.financial-value {
    font-weight: 600;
    color: #2d3748;
}

.discount-row .financial-value {
    color: #38a169;
}

.total-row {
    border-top: 2px solid rgba(0, 0, 0, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

.total-label {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2d3748;
}

.total-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
}

/* Items Card Full Width */
.items-card {
    grid-column: 1 / -1;
}

/* Items Table */
.items-table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.items-table thead {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.items-table th {
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    text-align: left;
}

.items-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.item-row:hover {
    background: rgba(102, 126, 234, 0.05);
}

.item-name {
    font-weight: 600;
    color: #2d3748;
}

.category-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.no-category {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.85rem;
}

.item-description {
    color: #718096;
    max-width: 200px;
    word-break: break-word;
}

.item-quantity {
    font-weight: 600;
    color: #4a5568;
}

.item-original-price, .item-special-price, .item-price, .item-total {
    font-weight: 600;
    color: #2d3748;
}

.item-original-price {
    text-decoration: line-through;
    color: #a0aec0;
    font-weight: 500;
}

.item-special-price {
    color: #38a169;
    font-weight: 700;
}

.item-discount {
    font-weight: 600;
    color: #e53e3e;
}

/* Empty State */
.empty-items-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #a0aec0;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-text {
    font-size: 1.1rem;
    margin: 0;
}

/* Footer Styles */
.receipt-details-footer {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border: none;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn-modern {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-edit {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-close-modal {
    background: linear-gradient(135deg, #a8a8a8 0%, #8c8c8c 100%);
    color: white;
}

.btn-close-modal:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(168, 168, 168, 0.4);
    color: white;
}

.btn-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.btn-text {
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .receipt-details-modal .modal-dialog {
        margin: 1rem;
        max-width: none;
    }
    
    .receipt-content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .receipt-header-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .receipt-details-body {
        padding: 1rem;
    }
    
    .items-table-container {
        font-size: 0.8rem;
    }
    
    .items-table th,
    .items-table td {
        padding: 0.5rem 0.4rem;
    }
}
