<?php
/**
 * 更新收據的客戶信息
 * KMS PC Receipt Maker
 */

// 啟用錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 記錄請求信息
    error_log('Update receipt customer request received');

    // 獲取JSON數據
    $input = file_get_contents('php://input');
    error_log("Raw input: $input");

    $data = json_decode($input, true);
    error_log('Decoded data: ' . print_r($data, true));

    if (!$data) {
        Response::error('無效的JSON數據');
    }

    // 驗證必需字段
    if (!isset($data['receipt_id']) || $data['receipt_id'] === '') {
        error_log('Missing receipt_id in data: ' . print_r($data, true));
        Response::error('收據ID不能為空');
    }

    // 清理輸入數據
    $receiptId = intval($data['receipt_id']);
    $customerName = trim($data['customer_name'] ?? '') ?: null; // 空字符串轉為 null
    $customerPhone = trim($data['customer_phone'] ?? '') ?: null;
    $customerEmail = trim($data['customer_email'] ?? '') ?: null;
    $customerAddress = trim($data['customer_address'] ?? '') ?: null;

    // 驗證郵箱格式（如果提供）
    if ($customerEmail && !Response::validateEmail($customerEmail)) {
        Response::error('郵箱格式不正確');
    }

    // 驗證手機號格式（如果提供）
    if ($customerPhone && !Response::validatePhone($customerPhone)) {
        Response::error('手機號格式不正確');
    }

    error_log("Processing update for receipt ID: $receiptId");

    $db = new Database();

    // 檢查收據是否存在
    $checkSql = "SELECT id, customer_name, customer_phone, customer_email, customer_address FROM receipts WHERE id = ?";
    $receipt = $db->fetch($checkSql, [$receiptId]);

    if (!$receipt) {
        Response::error('收據不存在，ID: ' . $receiptId);
    }

    error_log("Found receipt: " . print_r($receipt, true));

    // 更新客戶信息
    try {
        error_log("Attempting to update customer info for receipt $receiptId");

        // 獲取原始連接
        $connection = $db->getConnection();

        // 準備 SQL 語句
        $updateSql = "UPDATE receipts SET 
                      customer_name = ?, 
                      customer_phone = ?, 
                      customer_email = ?, 
                      customer_address = ?, 
                      updated_at = NOW() 
                      WHERE id = ?";
        $stmt = $connection->prepare($updateSql);

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $connection->error);
        }

        // 綁定參數
        $stmt->bind_param("ssssi", $customerName, $customerPhone, $customerEmail, $customerAddress, $receiptId);

        // 執行更新
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $affectedRows = $stmt->affected_rows;
        error_log("Update successful, affected rows: $affectedRows");

        $stmt->close();

        Response::success([
            'receipt_id' => $receiptId,
            'customer_name' => $customerName,
            'customer_phone' => $customerPhone,
            'customer_email' => $customerEmail,
            'customer_address' => $customerAddress,
            'affected_rows' => $affectedRows
        ], '客戶信息更新成功');

    } catch (Exception $e) {
        error_log("Update exception: " . $e->getMessage());
        Response::error('更新客戶信息失敗: ' . $e->getMessage());
    }
    
} catch (Exception $e) {
    error_log('Update receipt customer error: ' . $e->getMessage());
    Response::error('更新客戶信息失敗: ' . $e->getMessage());
}
